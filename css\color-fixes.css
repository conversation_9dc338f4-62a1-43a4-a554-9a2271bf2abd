/* ===== Color and Contrast Fixes ===== */

/* Global text improvements */
body {
    color: #374151 !important;
    font-weight: 400;
}

/* Header text improvements */
.header {
    background: #ffffff !important;
    border-bottom: 1px solid #e5e7eb;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.brand-name {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
    font-weight: 900 !important;
}

/* Navigation improvements */
.nav-link {
    color: #1f2937 !important;
    font-weight: 600 !important;
}

.nav-link:hover,
.nav-link.active {
    color: #2563eb !important;
}

/* Language button improvements */
.lang-btn {
    background: #ffffff !important;
    color: #374151 !important;
    border: 1px solid #d1d5db !important;
    font-weight: 600 !important;
}

.lang-btn:hover {
    background: #f9fafb !important;
    color: #2563eb !important;
    border-color: #2563eb !important;
}

/* Hero section text fixes */
.hero {
    background: linear-gradient(135deg, #1e40af 0%, #2563eb 100%) !important;
}

.hero-title {
    color: #ffffff !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.hero-title .gradient-text {
    background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
    text-shadow: none !important;
}

.hero-subtitle {
    color: rgba(255, 255, 255, 0.95) !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.hero-badge {
    background: rgba(255, 255, 255, 0.15) !important;
    color: #ffffff !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

/* Hero stats - Enhanced Design */
.hero-stats {
    display: flex !important;
    gap: 2rem !important;
    margin-bottom: 2rem !important;
    justify-content: center !important;
    flex-wrap: wrap !important;
}

.stat-item {
    background: rgba(255, 255, 255, 0.15) !important;
    backdrop-filter: blur(10px) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 1rem !important;
    padding: 1.5rem 2rem !important;
    text-align: center !important;
    min-width: 140px !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
}

.stat-item:hover {
    background: rgba(255, 255, 255, 0.25) !important;
    transform: translateY(-5px) !important;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2) !important;
    border-color: rgba(255, 255, 255, 0.4) !important;
}

.stat-number {
    color: #fbbf24 !important;
    font-size: 2.5rem !important;
    font-weight: 900 !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
    margin-bottom: 0.5rem !important;
    display: block !important;
    line-height: 1 !important;
}

.stat-label {
    color: rgba(255, 255, 255, 0.95) !important;
    font-size: 0.875rem !important;
    font-weight: 600 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
}

/* Hero cards text */
.floating-card {
    background: rgba(255, 255, 255, 0.95) !important;
    color: #1f2937 !important;
}

.floating-card h4 {
    color: #1f2937 !important;
    font-weight: 700 !important;
}

.floating-card p {
    color: #6b7280 !important;
}

/* Services section improvements - Fixed Colors */
.services {
    background: #f8fafc !important;
}

.services .section-title {
    color: #111827 !important;
    font-weight: 900 !important;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

.services .section-subtitle {
    color: #2563eb !important;
    font-weight: 600 !important;
    font-size: 1.125rem !important;
}

.services .section-badge {
    background: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%) !important;
    color: #ffffff !important;
    font-weight: 700 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
}

/* General section styling fallback */
.section-title {
    color: #111827 !important;
    font-weight: 900 !important;
}

.section-subtitle {
    color: #4b5563 !important;
    font-weight: 500 !important;
}

.section-badge {
    background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%) !important;
    color: #ffffff !important;
}

/* Service cards text */
.service-card h3 {
    color: #111827 !important;
    font-weight: 700 !important;
}

.service-card p {
    color: #4b5563 !important;
    font-weight: 400 !important;
}

.feature-item {
    color: #374151 !important;
    font-weight: 500 !important;
}

.feature-item i {
    font-weight: 900 !important;
}

/* Pricing section improvements - Enhanced Visibility */
.pricing {
    background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%) !important;
    position: relative;
}

.pricing::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 20%, rgba(37, 99, 235, 0.05) 0%, transparent 50%),
                radial-gradient(circle at 70% 80%, rgba(16, 185, 129, 0.05) 0%, transparent 50%);
    z-index: 1;
}

.pricing .container {
    position: relative;
    z-index: 2;
}

.pricing .section-title {
    color: #111827 !important;
    font-weight: 900 !important;
    font-size: 2.5rem !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.pricing .section-subtitle {
    color: #4b5563 !important;
    font-weight: 500 !important;
    font-size: 1.25rem !important;
}

.pricing .section-badge {
    background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%) !important;
    color: #ffffff !important;
    padding: 0.75rem 1.5rem !important;
    border-radius: 2rem !important;
    font-weight: 700 !important;
    box-shadow: 0 4px 15px rgba(37, 99, 235, 0.3) !important;
}

.pricing-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%) !important;
    border: 2px solid #e5e7eb !important;
    color: #1f2937 !important;
    border-radius: 1.5rem !important;
    padding: 2.5rem !important;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
    position: relative !important;
    overflow: hidden !important;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08) !important;
}

.pricing-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #e5e7eb, #d1d5db) !important;
    transition: all 0.3s ease;
}

.pricing-card.featured {
    border-color: #2563eb !important;
    background: linear-gradient(135deg, #ffffff 0%, #f0f9ff 100%) !important;
    transform: scale(1.05) !important;
    box-shadow: 0 20px 50px rgba(37, 99, 235, 0.15) !important;
}

.pricing-card.featured::before {
    background: linear-gradient(90deg, #2563eb, #3b82f6, #10b981, #f59e0b) !important;
}

.pricing-card:hover {
    transform: translateY(-8px) scale(1.02) !important;
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15) !important;
    border-color: #2563eb !important;
}

.pricing-card.featured:hover {
    transform: translateY(-8px) scale(1.07) !important;
}

.popular-badge {
    background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%) !important;
    color: #ffffff !important;
    padding: 0.5rem 1rem !important;
    border-radius: 1rem !important;
    font-weight: 700 !important;
    font-size: 0.875rem !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    box-shadow: 0 4px 15px rgba(37, 99, 235, 0.3) !important;
}

.plan-name {
    color: #111827 !important;
    font-weight: 700 !important;
    font-size: 1.5rem !important;
    margin-bottom: 1rem !important;
}

.plan-price {
    margin-bottom: 2rem !important;
}

.price-monthly {
    display: flex !important;
    align-items: baseline !important;
    justify-content: center !important;
    gap: 0.25rem !important;
    margin-bottom: 0.5rem !important;
}

.price-monthly .currency {
    color: #2563eb !important;
    font-weight: 700 !important;
    font-size: 1.25rem !important;
}

.price-monthly .amount {
    color: #2563eb !important;
    font-weight: 900 !important;
    font-size: 3rem !important;
    line-height: 1 !important;
    text-shadow: 0 2px 4px rgba(37, 99, 235, 0.1) !important;
}

.price-monthly .period {
    color: #6b7280 !important;
    font-weight: 600 !important;
    font-size: 1rem !important;
}

.price-yearly {
    color: #10b981 !important;
    font-weight: 600 !important;
    font-size: 0.875rem !important;
    background: rgba(16, 185, 129, 0.1) !important;
    padding: 0.25rem 0.75rem !important;
    border-radius: 0.5rem !important;
    display: inline-block !important;
}

.pricing-features {
    margin-bottom: 2rem !important;
}

.pricing-features .feature-item {
    color: #374151 !important;
    font-weight: 500 !important;
    padding: 0.75rem 0 !important;
    border-bottom: 1px solid #f3f4f6 !important;
    display: flex !important;
    align-items: center !important;
    gap: 0.75rem !important;
}

.pricing-features .feature-item:last-child {
    border-bottom: none !important;
}

.pricing-features .feature-item i {
    color: #10b981 !important;
    font-size: 1.125rem !important;
    flex-shrink: 0 !important;
}

.guarantee-text {
    color: #6b7280 !important;
    font-weight: 500 !important;
    font-size: 0.875rem !important;
    text-align: center !important;
    margin-top: 1rem !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 0.5rem !important;
}

.guarantee-text i {
    color: #10b981 !important;
}

/* Additional services pricing */
.additional-services .services-title {
    color: #111827 !important;
    font-weight: 700 !important;
}

.service-price-card {
    background: #ffffff !important;
    border: 1px solid #e5e7eb !important;
}

.service-price-card h4 {
    color: #111827 !important;
    font-weight: 700 !important;
}

.service-price {
    color: #4b5563 !important;
}

.service-price .price {
    color: #2563eb !important;
    font-weight: 900 !important;
}

/* Contact section improvements - Professional Support Design */
.contact {
    background: linear-gradient(135deg, #f8fafc 0%, #e5e7eb 100%) !important;
    position: relative;
    overflow: hidden;
}

.contact::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23e5e7eb" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>') !important;
    opacity: 0.3;
    z-index: 1;
}

.contact .container {
    position: relative;
    z-index: 2;
}

.contact .section-title {
    color: #111827 !important;
    font-weight: 900 !important;
    font-size: 2.5rem !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.contact .section-subtitle {
    color: #4b5563 !important;
    font-weight: 500 !important;
    font-size: 1.25rem !important;
}

.contact .section-badge {
    background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%) !important;
    color: #ffffff !important;
    padding: 0.75rem 1.5rem !important;
    border-radius: 2rem !important;
    font-weight: 700 !important;
    box-shadow: 0 4px 15px rgba(37, 99, 235, 0.3) !important;
}

.contact-info {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)) !important;
    gap: 2rem !important;
    margin-bottom: 3rem !important;
}

.contact-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%) !important;
    border: 2px solid #e5e7eb !important;
    border-radius: 1.5rem !important;
    padding: 2.5rem !important;
    text-align: center !important;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
    position: relative !important;
    overflow: hidden !important;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08) !important;
}

.contact-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #2563eb, #3b82f6, #10b981) !important;
    transform: scaleX(0);
    transition: transform 0.4s ease;
}

.contact-card:hover::before {
    transform: scaleX(1);
}

.contact-card:hover {
    transform: translateY(-8px) scale(1.02) !important;
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15) !important;
    border-color: #2563eb !important;
    background: linear-gradient(135deg, #ffffff 0%, #f0f9ff 100%) !important;
}

.contact-card .icon {
    margin-bottom: 1.5rem !important;
    transform: scale(1) !important;
    transition: transform 0.3s ease !important;
}

.contact-card:hover .icon {
    transform: scale(1.1) !important;
}

.contact-card h4 {
    color: #111827 !important;
    font-weight: 700 !important;
    margin-bottom: 1rem !important;
    font-size: 1.25rem !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important;
}

.contact-card p {
    color: #4b5563 !important;
    font-weight: 500 !important;
    margin-bottom: 1.5rem !important;
    font-size: 1.125rem !important;
    direction: ltr !important;
    text-align: center !important;
}

.contact-card .btn {
    background: linear-gradient(135deg, transparent 0%, rgba(37, 99, 235, 0.05) 100%) !important;
    color: #2563eb !important;
    border: 2px solid #2563eb !important;
    font-weight: 600 !important;
    padding: 0.75rem 1.5rem !important;
    border-radius: 0.75rem !important;
    transition: all 0.3s ease !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    font-size: 0.875rem !important;
}

.contact-card .btn:hover {
    background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%) !important;
    color: #ffffff !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3) !important;
    border-color: #2563eb !important;
}

/* Contact form improvements - Beautiful Design */
.contact-form-wrapper {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%) !important;
    border: 2px solid #e5e7eb !important;
    border-radius: 1.5rem !important;
    padding: 2.5rem !important;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1) !important;
    position: relative;
    overflow: hidden;
}

.contact-form-wrapper::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #2563eb, #3b82f6, #10b981, #f59e0b);
    border-radius: 1.5rem 1.5rem 0 0;
}

.contact-form h3 {
    color: #111827 !important;
    font-weight: 700 !important;
    margin-bottom: 1.5rem;
    text-align: center;
    font-size: 1.5rem;
}

/* Form improvements */
.form-group label {
    color: #374151 !important;
    font-weight: 600 !important;
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
    display: block;
}

.form-group input,
.form-group textarea {
    color: #1f2937 !important;
    border: 2px solid #d1d5db !important;
    background: #ffffff !important;
    border-radius: 0.5rem !important;
    padding: 0.75rem 1rem !important;
    font-size: 1rem !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
    width: 100% !important;
}

.form-group input:focus,
.form-group textarea:focus {
    border-color: #2563eb !important;
    color: #1f2937 !important;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1) !important;
    outline: none !important;
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: #9ca3af !important;
    font-weight: 400 !important;
}

.contact-form .btn {
    background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%) !important;
    color: #ffffff !important;
    border: none !important;
    padding: 1rem 2rem !important;
    font-size: 1rem !important;
    font-weight: 700 !important;
    border-radius: 0.75rem !important;
    width: 100% !important;
    margin-top: 1rem !important;
}

.contact-form .btn:hover {
    background: linear-gradient(135deg, #1d4ed8 0%, #2563eb 100%) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(37, 99, 235, 0.25) !important;
}

/* Hero cards z-index and positioning fixes */
.hero-visual .hero-cards {
    position: relative;
    z-index: 10;
}

.floating-card {
    position: relative;
    z-index: 11;
    isolation: isolate;
}

/* Language switcher improvements */
.language-switcher {
    position: fixed !important;
    top: 1rem !important;
    right: 1.5rem !important;
    z-index: 9999 !important;
}

/* Prevent overlapping issues */
.hero-content > * {
    position: relative;
    z-index: 5;
}

.hero-stats {
    position: relative;
    z-index: 6;
}

/* Better spacing for hero elements */
.hero-text {
    padding-right: 2rem;
}

.hero-visual {
    padding-left: 2rem;
}

/* Social Media Icons - Enhanced Visibility */
.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 1.5rem;
}

.social-link {
    width: 45px !important;
    height: 45px !important;
    background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%) !important;
    border: 2px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    color: #ffffff !important;
    transition: all 0.3s ease !important;
    text-decoration: none !important;
    font-size: 1.125rem !important;
}

.social-link:hover {
    background: linear-gradient(135deg, #1d4ed8 0%, #2563eb 100%) !important;
    border-color: rgba(255, 255, 255, 0.4) !important;
    transform: translateY(-3px) scale(1.05) !important;
    box-shadow: 0 8px 25px rgba(37, 99, 235, 0.4) !important;
    color: #ffffff !important;
}

.social-link.facebook {
    background: linear-gradient(135deg, #1877f2 0%, #42a5f5 100%) !important;
}

.social-link.facebook:hover {
    background: linear-gradient(135deg, #166fe5 0%, #1877f2 100%) !important;
    box-shadow: 0 8px 25px rgba(24, 119, 242, 0.4) !important;
}

.social-link.twitter {
    background: linear-gradient(135deg, #1da1f2 0%, #42a5f5 100%) !important;
}

.social-link.twitter:hover {
    background: linear-gradient(135deg, #1a91da 0%, #1da1f2 100%) !important;
    box-shadow: 0 8px 25px rgba(29, 161, 242, 0.4) !important;
}

.social-link.instagram {
    background: linear-gradient(135deg, #e4405f 0%, #f56040 100%) !important;
}

.social-link.instagram:hover {
    background: linear-gradient(135deg, #d73447 0%, #e4405f 100%) !important;
    box-shadow: 0 8px 25px rgba(228, 64, 95, 0.4) !important;
}

.social-link.linkedin {
    background: linear-gradient(135deg, #0077b5 0%, #00a0dc 100%) !important;
}

.social-link.linkedin:hover {
    background: linear-gradient(135deg, #005885 0%, #0077b5 100%) !important;
    box-shadow: 0 8px 25px rgba(0, 119, 181, 0.4) !important;
}

/* Footer improvements */
.footer {
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%) !important;
    color: #ffffff !important;
}

.footer-section h3,
.footer-section h4 {
    color: #ffffff !important;
    font-weight: 700 !important;
}

.footer-section p {
    color: rgba(255, 255, 255, 0.8) !important;
    font-weight: 400 !important;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.8) !important;
    font-weight: 500 !important;
}

.footer-links a:hover {
    color: #60a5fa !important;
}

.footer-bottom p {
    color: rgba(255, 255, 255, 0.7) !important;
}

.footer-bottom a {
    color: #60a5fa !important;
    font-weight: 600 !important;
}

.contact-item {
    color: rgba(255, 255, 255, 0.8) !important;
    font-weight: 500 !important;
}

.contact-item i {
    color: #60a5fa !important;
}

/* Button text improvements */
.btn {
    font-weight: 700 !important;
}

.btn-primary {
    color: #ffffff !important;
}

.btn-secondary {
    color: #ffffff !important;
}

.btn-accent {
    color: #ffffff !important;
}

.btn-outline {
    color: #2563eb !important;
}

.btn-outline:hover {
    color: #ffffff !important;
}

/* Back to top button */
.back-to-top {
    background: #2563eb !important;
    color: #ffffff !important;
}

/* Scroll indicator */
.scroll-arrow {
    color: rgba(255, 255, 255, 0.8) !important;
    border-color: rgba(255, 255, 255, 0.5) !important;
}

.scroll-arrow:hover {
    color: #ffffff !important;
    border-color: #ffffff !important;
}

/* Mobile menu improvements */
.mobile-menu-toggle span {
    background: #374151 !important;
}

/* Ensure all text is readable */
* {
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Mobile Responsive Improvements */
@media (max-width: 768px) {
    .hero-stats {
        grid-template-columns: repeat(3, 1fr) !important;
        gap: 1rem !important;
        margin-bottom: 2rem !important;
    }

    .stat-item {
        padding: 1rem 1.25rem !important;
        min-width: auto !important;
    }

    .stat-number {
        font-size: 1.75rem !important;
    }

    .stat-label {
        font-size: 0.75rem !important;
    }

    .contact-info {
        grid-template-columns: 1fr !important;
        gap: 1.5rem !important;
    }

    .contact-card {
        padding: 2rem !important;
    }

    .pricing-card {
        padding: 2rem !important;
    }

    .pricing-card.featured {
        transform: none !important;
    }

    .pricing-card.featured:hover {
        transform: translateY(-4px) !important;
    }

    .price-monthly .amount {
        font-size: 2.5rem !important;
    }

    .hero-text {
        padding-right: 0 !important;
    }

    .hero-visual {
        padding-left: 0 !important;
        margin-top: 2rem !important;
    }
}

@media (max-width: 480px) {
    .hero-stats {
        grid-template-columns: 1fr !important;
        gap: 1rem !important;
        max-width: 300px !important;
        margin: 0 auto 2rem !important;
    }

    .stat-item {
        padding: 1.25rem !important;
    }

    .stat-number {
        font-size: 2rem !important;
    }

    .stat-label {
        font-size: 0.875rem !important;
    }

    .contact-card {
        padding: 1.5rem !important;
    }

    .pricing-card {
        padding: 1.5rem !important;
    }

    .price-monthly .amount {
        font-size: 2rem !important;
    }
}

/* High contrast for accessibility */
@media (prefers-contrast: high) {
    .hero-title .gradient-text {
        -webkit-text-fill-color: #fbbf24 !important;
    }

    .brand-name {
        -webkit-text-fill-color: #2563eb !important;
    }

    .stat-item {
        background: rgba(255, 255, 255, 0.3) !important;
        border-color: rgba(255, 255, 255, 0.5) !important;
    }

    .stat-number {
        color: #ffffff !important;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5) !important;
    }

    .stat-label {
        color: #ffffff !important;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
    }
}

/* Dark mode text fixes */
@media (prefers-color-scheme: dark) {
    .section-title {
        color: #f9fafb !important;
    }
    
    .section-subtitle {
        color: #d1d5db !important;
    }
    
    .service-card h3 {
        color: #f9fafb !important;
    }
    
    .service-card p {
        color: #d1d5db !important;
    }
}
