/* ===== CSS Variables ===== */
:root {
    /* Colors */
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --primary-light: #3b82f6;
    --secondary-color: #10b981;
    --accent-color: #f59e0b;
    --dark-color: #1f2937;
    --light-color: #f8fafc;
    --white: #ffffff;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    --gradient-secondary: linear-gradient(135deg, var(--secondary-color) 0%, var(--primary-color) 100%);
    --gradient-accent: linear-gradient(135deg, var(--accent-color) 0%, var(--secondary-color) 100%);
    
    /* Typography */
    --font-arabic: 'Cairo', sans-serif;
    --font-english: 'Inter', sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

/* ===== Reset & Base Styles ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: var(--font-arabic);
    line-height: 1.6;
    color: var(--gray-800);
    background-color: var(--white);
    overflow-x: hidden;
}

body[dir="ltr"] {
    font-family: var(--font-english);
}

/* ===== Typography ===== */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: var(--spacing-md);
}

h1 { font-size: var(--font-size-5xl); }
h2 { font-size: var(--font-size-4xl); }
h3 { font-size: var(--font-size-3xl); }
h4 { font-size: var(--font-size-2xl); }
h5 { font-size: var(--font-size-xl); }
h6 { font-size: var(--font-size-lg); }

p {
    margin-bottom: var(--spacing-md);
    color: var(--gray-600);
}

a {
    text-decoration: none;
    color: inherit;
    transition: var(--transition-normal);
}

/* ===== Utility Classes ===== */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

.gradient-text {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* ===== Buttons ===== */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-xl);
    border: none;
    border-radius: var(--radius-lg);
    font-weight: 600;
    font-size: var(--font-size-base);
    cursor: pointer;
    transition: var(--transition-normal);
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: var(--gradient-primary);
    color: var(--white);
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-outline:hover {
    background: var(--primary-color);
    color: var(--white);
}

.btn-lg {
    padding: var(--spacing-lg) var(--spacing-2xl);
    font-size: var(--font-size-lg);
}

/* ===== Language Switcher ===== */
.language-switcher {
    position: fixed;
    top: var(--spacing-lg);
    left: var(--spacing-lg);
    z-index: 1000;
}

.lang-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: var(--transition-normal);
    box-shadow: var(--shadow-sm);
}

.lang-btn:hover {
    background: var(--gray-50);
    box-shadow: var(--shadow-md);
}

/* ===== Header ===== */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--gray-200);
    z-index: 999;
    transition: var(--transition-normal);
}

.navbar {
    padding: var(--spacing-md) 0;
}

.navbar .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.logo {
    height: 40px;
    width: auto;
}

.brand-name {
    font-size: var(--font-size-2xl);
    font-weight: 900;
    color: var(--primary-color);
    margin: 0;
}

.nav-menu {
    display: flex;
    align-items: center;
}

.nav-list {
    display: flex;
    list-style: none;
    gap: var(--spacing-xl);
    margin: 0;
}

.nav-link {
    font-weight: 500;
    color: var(--gray-700);
    padding: var(--spacing-sm) 0;
    position: relative;
    transition: var(--transition-normal);
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: var(--transition-normal);
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

.dropdown {
    position: relative;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    padding: var(--spacing-md);
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: var(--transition-normal);
    list-style: none;
}

.dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-menu li {
    margin: 0;
}

.dropdown-menu a {
    display: block;
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--gray-700);
    border-radius: var(--radius-md);
    transition: var(--transition-normal);
}

.dropdown-menu a:hover {
    background: var(--gray-100);
    color: var(--primary-color);
}

.nav-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    gap: 4px;
    background: none;
    border: none;
    cursor: pointer;
    padding: var(--spacing-sm);
}

.mobile-menu-toggle span {
    width: 25px;
    height: 3px;
    background: var(--gray-700);
    border-radius: 2px;
    transition: var(--transition-normal);
}

/* ===== Hero Section ===== */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    opacity: 0.9;
}

.hero-particles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
        radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
    animation: float 20s ease-in-out infinite;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-3xl);
    align-items: center;
    position: relative;
    z-index: 2;
}

.hero-text {
    color: var(--white);
}

.hero-title {
    font-size: var(--font-size-5xl);
    font-weight: 900;
    line-height: 1.1;
    margin-bottom: var(--spacing-lg);
}

.hero-title span {
    display: block;
}

.hero-subtitle {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-2xl);
    opacity: 0.9;
}

.hero-buttons {
    display: flex;
    gap: var(--spacing-lg);
    flex-wrap: wrap;
}

.hero-image {
    position: relative;
}

.hero-graphic {
    position: relative;
    height: 500px;
}

/* Hero visual elements removed - using new grid design */

.scroll-indicator {
    position: absolute;
    bottom: var(--spacing-xl);
    left: 50%;
    transform: translateX(-50%);
    color: var(--white);
    opacity: 0.7;
}

.scroll-arrow {
    width: 2px;
    height: 30px;
    background: var(--white);
    margin: 0 auto;
    position: relative;
    animation: scroll-bounce 2s infinite;
}

.scroll-arrow::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: -3px;
    width: 8px;
    height: 8px;
    border-right: 2px solid var(--white);
    border-bottom: 2px solid var(--white);
    transform: rotate(45deg);
}

/* ===== Sections ===== */
.section-header {
    text-align: center;
    margin-bottom: var(--spacing-3xl);
}

.section-title {
    font-size: var(--font-size-4xl);
    color: var(--gray-900);
    margin-bottom: var(--spacing-md);
}

.section-subtitle {
    font-size: var(--font-size-lg);
    color: var(--gray-600);
    max-width: 600px;
    margin: 0 auto;
}

/* ===== Features Section ===== */
.features {
    padding: var(--spacing-3xl) 0;
    background: var(--gray-50);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-2xl);
}

.feature-card {
    background: var(--white);
    padding: var(--spacing-2xl);
    border-radius: var(--radius-xl);
    text-align: center;
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-lg);
    color: var(--white);
    font-size: var(--font-size-2xl);
}

.feature-card h3 {
    color: var(--gray-900);
    margin-bottom: var(--spacing-md);
}

/* ===== Services Section ===== */
.services {
    padding: var(--spacing-3xl) 0;
}

.services-tabs {
    max-width: 1000px;
    margin: 0 auto;
}

.tab-buttons {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-2xl);
    flex-wrap: wrap;
}

.tab-btn {
    padding: var(--spacing-md) var(--spacing-xl);
    background: var(--gray-100);
    border: none;
    border-radius: var(--radius-lg);
    font-weight: 600;
    color: var(--gray-700);
    cursor: pointer;
    transition: var(--transition-normal);
}

.tab-btn.active,
.tab-btn:hover {
    background: var(--primary-color);
    color: var(--white);
}

.tab-content {
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: var(--spacing-2xl);
    box-shadow: var(--shadow-lg);
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

/* ===== Services Grid ===== */
.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
    margin-top: var(--spacing-xl);
}

.service-card {
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: var(--spacing-2xl);
    text-align: center;
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
    border: 1px solid var(--gray-200);
}

.service-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-color);
}

.service-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-lg);
    color: var(--white);
    font-size: var(--font-size-2xl);
}

.service-card h3 {
    color: var(--gray-900);
    margin-bottom: var(--spacing-md);
    font-size: var(--font-size-xl);
}

.service-card p {
    color: var(--gray-600);
    margin-bottom: var(--spacing-lg);
    line-height: 1.6;
}

.service-features {
    list-style: none;
    margin-bottom: var(--spacing-xl);
    text-align: right;
}

.service-features li {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
    color: var(--gray-700);
    font-size: var(--font-size-sm);
}

.service-features i {
    color: var(--secondary-color);
    font-size: var(--font-size-sm);
}

/* ===== Pricing Section ===== */
.pricing {
    padding: var(--spacing-3xl) 0;
    background: var(--gray-50);
}

.pricing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
    margin-top: var(--spacing-2xl);
}

.pricing-card {
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: var(--spacing-2xl);
    text-align: center;
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
    position: relative;
    border: 2px solid transparent;
}

.pricing-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.pricing-card.featured {
    border-color: var(--primary-color);
    transform: scale(1.05);
}

.pricing-card.featured:hover {
    transform: scale(1.05) translateY(-5px);
}

.featured-badge {
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--gradient-primary);
    color: var(--white);
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-sm);
    font-weight: 600;
}

.pricing-header {
    margin-bottom: var(--spacing-xl);
}

.pricing-header h3 {
    font-size: var(--font-size-xl);
    color: var(--gray-900);
    margin-bottom: var(--spacing-md);
}

.price {
    display: flex;
    align-items: baseline;
    justify-content: center;
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-md);
}

.currency {
    font-size: var(--font-size-lg);
    color: var(--primary-color);
    font-weight: 600;
}

.amount {
    font-size: var(--font-size-4xl);
    font-weight: 900;
    color: var(--primary-color);
}

.period {
    font-size: var(--font-size-base);
    color: var(--gray-600);
}

.pricing-header p {
    color: var(--gray-600);
    font-size: var(--font-size-sm);
    margin: 0;
}

.pricing-features {
    list-style: none;
    margin-bottom: var(--spacing-xl);
    text-align: right;
}

.pricing-features li {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
    color: var(--gray-700);
}

.pricing-features i {
    color: var(--secondary-color);
    font-size: var(--font-size-sm);
}

/* ===== About Section ===== */
.about {
    padding: var(--spacing-3xl) 0;
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-3xl);
    align-items: center;
}

.about-text h2 {
    margin-bottom: var(--spacing-lg);
}

.about-text p {
    font-size: var(--font-size-lg);
    line-height: 1.8;
    margin-bottom: var(--spacing-lg);
}

.about-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-lg);
    margin-top: var(--spacing-xl);
}

.stat-item {
    text-align: center;
    padding: var(--spacing-lg);
    background: var(--gray-50);
    border-radius: var(--radius-lg);
}

.stat-number {
    font-size: var(--font-size-3xl);
    font-weight: 900;
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    font-weight: 600;
}

.about-image {
    position: relative;
}

.image-container {
    position: relative;
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-xl);
}

.about-img {
    width: 100%;
    height: 400px;
    object-fit: cover;
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(37, 99, 235, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition-normal);
}

.image-container:hover .image-overlay {
    opacity: 1;
}

.play-button {
    width: 80px;
    height: 80px;
    background: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    font-size: var(--font-size-2xl);
    cursor: pointer;
    transition: var(--transition-normal);
}

.play-button:hover {
    transform: scale(1.1);
}

/* ===== Contact Section ===== */
.contact {
    padding: var(--spacing-3xl) 0;
    background: var(--gray-50);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-3xl);
    margin-top: var(--spacing-2xl);
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

.contact-item {
    display: flex;
    gap: var(--spacing-lg);
    align-items: flex-start;
}

.contact-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: var(--font-size-xl);
    flex-shrink: 0;
}

.contact-details h4 {
    color: var(--gray-900);
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-size-lg);
}

.contact-details p {
    color: var(--gray-600);
    margin: 0;
    line-height: 1.6;
}

.contact-form-wrapper {
    background: var(--white);
    padding: var(--spacing-2xl);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
}

.contact-form .form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.contact-form .form-group {
    margin-bottom: var(--spacing-lg);
}

.contact-form input,
.contact-form textarea {
    width: 100%;
    padding: var(--spacing-md);
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-base);
    transition: var(--transition-normal);
    background: var(--white);
    font-family: inherit;
}

.contact-form input:focus,
.contact-form textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.contact-form textarea {
    resize: vertical;
    min-height: 120px;
}

/* ===== Footer ===== */
.footer {
    background: var(--gray-900);
    color: var(--white);
    padding: var(--spacing-3xl) 0 var(--spacing-lg);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-2xl);
    margin-bottom: var(--spacing-2xl);
}

.footer-section h3,
.footer-section h4 {
    color: var(--white);
    margin-bottom: var(--spacing-lg);
    font-size: var(--font-size-lg);
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.footer-logo .logo {
    height: 40px;
    width: auto;
}

.footer-section p {
    color: var(--gray-300);
    line-height: 1.6;
    margin-bottom: var(--spacing-lg);
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: var(--spacing-sm);
}

.footer-section ul li a {
    color: var(--gray-300);
    transition: var(--transition-normal);
    text-decoration: none;
}

.footer-section ul li a:hover {
    color: var(--primary-color);
}

.social-links {
    display: flex;
    gap: var(--spacing-md);
}

.social-links a {
    width: 40px;
    height: 40px;
    background: var(--gray-800);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--gray-300);
    transition: var(--transition-normal);
    text-decoration: none;
}

.social-links a:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: translateY(-2px);
}

.contact-info-footer {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.contact-info-footer p {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin: 0;
    color: var(--gray-300);
}

.contact-info-footer i {
    color: var(--primary-color);
    width: 20px;
}

.footer-bottom {
    border-top: 1px solid var(--gray-800);
    padding-top: var(--spacing-lg);
}

.footer-bottom-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.footer-bottom p {
    color: var(--gray-400);
    margin: 0;
    font-size: var(--font-size-sm);
}

.footer-bottom a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
}

.footer-bottom a:hover {
    text-decoration: underline;
}

/* ===== Back to Top Button ===== */
.back-to-top {
    position: fixed;
    bottom: var(--spacing-xl);
    left: var(--spacing-xl);
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    border: none;
    border-radius: 50%;
    color: var(--white);
    font-size: var(--font-size-lg);
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-normal);
    z-index: 1000;
    box-shadow: var(--shadow-lg);
}

.back-to-top.show {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-xl);
}

/* ===== Password Strength Indicator ===== */
.password-strength {
    margin-top: var(--spacing-sm);
    display: none;
}

.strength-bar {
    width: 100%;
    height: 4px;
    background: var(--gray-200);
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: var(--spacing-xs);
}

.strength-fill {
    height: 100%;
    transition: var(--transition-normal);
    border-radius: 2px;
}

.strength-fill.strength-weak {
    background: #dc2626;
}

.strength-fill.strength-medium {
    background: #f59e0b;
}

.strength-fill.strength-strong {
    background: #10b981;
}

.strength-fill.strength-very-strong {
    background: var(--secondary-color);
}

.strength-text {
    font-size: var(--font-size-xs);
    color: var(--gray-600);
}

/* ===== Form Row for Register ===== */
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
}

/* ===== Additional Responsive Styles ===== */
@media (max-width: 768px) {
    .services-grid,
    .pricing-grid {
        grid-template-columns: 1fr;
    }

    .about-content,
    .contact-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
    }

    .about-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-md);
    }

    .footer-content {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-lg);
    }

    .footer-bottom-content {
        flex-direction: column;
        text-align: center;
    }

    .contact-form .form-row,
    .form-row {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .pricing-card.featured {
        transform: none;
    }

    .pricing-card.featured:hover {
        transform: translateY(-5px);
    }
}

@media (max-width: 480px) {
    .footer-content {
        grid-template-columns: 1fr;
    }

    .about-stats {
        grid-template-columns: 1fr;
    }

    .social-links {
        justify-content: center;
    }

    .back-to-top {
        bottom: var(--spacing-md);
        left: var(--spacing-md);
        width: 45px;
        height: 45px;
    }
}

/* ===== Additional Styles for Enhanced UX ===== */

/* Loading State */
.loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--gray-300);
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Form Error States */
.form-group input.error,
.form-group textarea.error {
    border-color: #dc2626;
    box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

.field-error {
    color: #dc2626;
    font-size: var(--font-size-xs);
    margin-top: var(--spacing-xs);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.field-error::before {
    content: '⚠';
    font-size: var(--font-size-sm);
}

/* Success States */
.form-group input.success,
.form-group textarea.success {
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

/* Skeleton Loading */
.skeleton {
    background: linear-gradient(90deg, var(--gray-200) 25%, var(--gray-100) 50%, var(--gray-200) 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
}

@keyframes skeleton-loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* Tooltip */
.tooltip {
    position: relative;
    cursor: help;
}

.tooltip::before {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--gray-900);
    color: var(--white);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    font-size: var(--font-size-xs);
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-normal);
    z-index: 1000;
}

.tooltip::after {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: var(--gray-900);
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-normal);
}

.tooltip:hover::before,
.tooltip:hover::after {
    opacity: 1;
    visibility: visible;
}

/* Badge */
.badge {
    display: inline-flex;
    align-items: center;
    padding: var(--spacing-xs) var(--spacing-sm);
    background: var(--primary-color);
    color: var(--white);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge.badge-success {
    background: var(--secondary-color);
}

.badge.badge-warning {
    background: var(--accent-color);
}

.badge.badge-error {
    background: #dc2626;
}

/* Progress Bar */
.progress {
    width: 100%;
    height: 8px;
    background: var(--gray-200);
    border-radius: 4px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: var(--gradient-primary);
    border-radius: 4px;
    transition: width 0.3s ease;
}

/* Modal Overlay */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-normal);
}

.modal-overlay.show {
    opacity: 1;
    visibility: visible;
}

.modal {
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: var(--spacing-2xl);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.8);
    transition: var(--transition-normal);
}

.modal-overlay.show .modal {
    transform: scale(1);
}

/* Accordion */
.accordion-item {
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    margin-bottom: var(--spacing-md);
    overflow: hidden;
}

.accordion-header {
    padding: var(--spacing-lg);
    background: var(--gray-50);
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: var(--transition-normal);
}

.accordion-header:hover {
    background: var(--gray-100);
}

.accordion-header.active {
    background: var(--primary-color);
    color: var(--white);
}

.accordion-content {
    padding: 0 var(--spacing-lg);
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease, padding 0.3s ease;
}

.accordion-content.active {
    padding: var(--spacing-lg);
    max-height: 500px;
}

/* Tabs Alternative Style */
.tabs-vertical {
    display: flex;
    gap: var(--spacing-xl);
}

.tabs-vertical .tab-buttons {
    flex-direction: column;
    min-width: 200px;
}

.tabs-vertical .tab-content {
    flex: 1;
}

/* Card Hover Effects */
.card-hover-lift {
    transition: var(--transition-normal);
}

.card-hover-lift:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
}

.card-hover-glow {
    transition: var(--transition-normal);
}

.card-hover-glow:hover {
    box-shadow: 0 0 30px rgba(37, 99, 235, 0.3);
}

/* Text Utilities */
.text-gradient {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Border Utilities */
.border-gradient {
    border: 2px solid transparent;
    background: linear-gradient(var(--white), var(--white)) padding-box,
                var(--gradient-primary) border-box;
}

/* Glassmorphism Effect */
.glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Neumorphism Effect */
.neomorphism {
    background: var(--gray-100);
    box-shadow:
        8px 8px 16px rgba(163, 177, 198, 0.6),
        -8px -8px 16px rgba(255, 255, 255, 0.5);
}

.neomorphism-inset {
    background: var(--gray-100);
    box-shadow:
        inset 8px 8px 16px rgba(163, 177, 198, 0.6),
        inset -8px -8px 16px rgba(255, 255, 255, 0.5);
}

/* Print Styles */
@media print {
    .no-print {
        display: none !important;
    }

    .print-only {
        display: block !important;
    }

    * {
        color: black !important;
        background: white !important;
    }

    .container {
        max-width: none !important;
        padding: 0 !important;
    }
}

/* ===== Responsive Design ===== */
@media (max-width: 768px) {
    .container {
        padding: 0 var(--spacing-md);
    }
    
    .nav-menu {
        display: none;
    }
    
    .mobile-menu-toggle {
        display: flex;
    }
    
    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: var(--spacing-2xl);
    }
    
    .hero-title {
        font-size: var(--font-size-3xl);
    }
    
    .hero-buttons {
        justify-content: center;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
    }
    
    .tab-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .tab-btn {
        width: 100%;
        max-width: 300px;
    }
}
